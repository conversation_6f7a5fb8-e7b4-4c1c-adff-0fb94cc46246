// Main Application Logic - Enhanced for Modern UI
class CaptionStudio {
    constructor() {
        this.videoFile = null;
        this.transcriptionData = null;
        this.currentTime = 0;
        this.isPlaying = false;
        this.timelineZoom = 1;
        this.selectedWordBlock = null;
        this.isInitialized = false;
        this.wordGroupingEnabled = false;

        // Enhanced state management
        this.state = {
            currentView: 'upload', // 'upload' or 'editor'
            isProcessing: false,
            hasVideo: false,
            hasTranscription: false
        };

        // Animation and UI settings
        this.animations = {
            enabled: true,
            duration: 300
        };

        this.initializeElements();
        this.setupEventListeners();
        this.setupCollapsiblePanels();

        // Initialize video player component (optional)
        try {
            if (typeof VideoPlayer !== 'undefined') {
                this.videoPlayerComponent = new VideoPlayer(this);
                console.log('VideoPlayer component initialized');
            } else {
                console.warn('VideoPlayer class not available, using fallback');
            }
        } catch (error) {
            console.warn('Failed to initialize VideoPlayer component:', error);
        }

        this.updateStatus('Ready to upload video');
        this.isInitialized = true;
    }

    initializeElements() {
        try {
            // Upload elements
            this.uploadSection = document.getElementById('uploadSection');
            this.editorSection = document.getElementById('editorSection');
            this.uploadArea = document.getElementById('uploadArea');
            this.videoInput = document.getElementById('videoInput');
            this.browseBtn = document.getElementById('browseBtn');
            this.fileInfo = document.getElementById('fileInfo');
            this.progressContainer = document.getElementById('progressContainer');
            this.progressFill = document.getElementById('progressFill');
            this.progressText = document.getElementById('progressText');
            this.progressPercentage = document.getElementById('progressPercentage');

            // Video elements
            this.videoPlayer = document.getElementById('videoPlayer');
            this.captionOverlay = document.getElementById('captionOverlay');

        // Control elements (optional - may not exist in simplified UI)
        this.playPauseBtn = document.getElementById('playPauseBtn');
        this.currentTimeDisplay = document.getElementById('currentTime');
        this.totalTimeDisplay = document.getElementById('totalTime');
        this.seekBar = document.getElementById('seekBar');
        this.volumeBar = document.getElementById('volumeBar');
        this.muteBtn = document.getElementById('muteBtn');

        // Timeline elements
        this.timelineContainer = document.getElementById('timelineContainer');
        this.timelineTrack = document.getElementById('timelineTrack');
        this.playhead = document.getElementById('playhead');
        this.timelineRuler = document.getElementById('timelineRuler');

        // Style controls
        this.fontFamily = document.getElementById('fontFamily');
        this.fontSize = document.getElementById('fontSize');
        this.fontSizeValue = document.getElementById('fontSizeValue');
        this.textUppercase = document.getElementById('textUppercase');
        this.fontColor = document.getElementById('fontColor');
        this.backgroundColor = document.getElementById('backgroundColor');
        this.showBackground = document.getElementById('showBackground');
        this.positionX = document.getElementById('positionX');
        this.positionXValue = document.getElementById('positionXValue');
        this.positionY = document.getElementById('positionY');
        this.positionYValue = document.getElementById('positionYValue');
        this.captionWidth = document.getElementById('captionWidth');
        this.captionWidthValue = document.getElementById('captionWidthValue');

        // Effect controls
        this.videoEffect = document.getElementById('videoEffect');
        this.effectIntensity = document.getElementById('effectIntensity');
        this.effectIntensityValue = document.getElementById('effectIntensityValue');

        // Transcript elements
        this.transcriptLoading = document.getElementById('transcriptLoading');
        this.transcriptContent = document.getElementById('transcriptContent');

        // Action buttons
        this.exportBtn = document.getElementById('exportBtn');
        this.saveProjectBtn = document.getElementById('saveProjectBtn');
        this.zoomInBtn = document.getElementById('zoomInBtn');
        this.zoomOutBtn = document.getElementById('zoomOutBtn');
        this.resetZoomBtn = document.getElementById('resetZoomBtn');
        this.wordGroupingToggle = document.getElementById('wordGroupingToggle');

            // Status elements
            this.statusText = document.getElementById('statusText');
            this.statusIcon = document.getElementById('statusIcon');
            this.statusInfo = document.getElementById('statusInfo');

            // Validate critical elements with detailed logging
            const missingElements = [];
            if (!this.uploadArea) missingElements.push('uploadArea');
            if (!this.videoPlayer) missingElements.push('videoPlayer');
            if (!this.statusText) missingElements.push('statusText');

            if (missingElements.length > 0) {
                throw new Error(`Critical UI elements not found: ${missingElements.join(', ')}`);
            }

            console.log('All critical elements found successfully');

        } catch (error) {
            console.error('Failed to initialize elements:', error);
            this.showError('Failed to initialize application. Please refresh the page.');
        }
    }

    setupCollapsiblePanels() {
        // Setup collapsible panel functionality
        const panelHeaders = document.querySelectorAll('.panel-header');
        panelHeaders.forEach(header => {
            const toggle = header.querySelector('.panel-toggle');
            const content = header.nextElementSibling;

            if (toggle && content) {
                header.addEventListener('click', () => {
                    const isCollapsed = content.style.display === 'none';
                    content.style.display = isCollapsed ? 'block' : 'none';
                    toggle.innerHTML = isCollapsed ? '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';

                    // Add animation class
                    if (this.animations.enabled) {
                        content.classList.toggle('fade-in', isCollapsed);
                    }
                });

                // Set initial state - first panel collapsed by default
                const isFirstPanel = header.closest('.control-panel') === document.querySelector('.control-panel');
                if (isFirstPanel) {
                    content.style.display = 'none';
                    toggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
                }
            }
        });
    }

    setupEventListeners() {
        try {
        // Upload events
        this.browseBtn.addEventListener('click', () => this.videoInput.click());
        this.videoInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));

        // Video events
        this.videoPlayer.addEventListener('loadedmetadata', () => this.handleVideoLoaded());
        this.videoPlayer.addEventListener('timeupdate', () => this.handleTimeUpdate());
        this.videoPlayer.addEventListener('play', () => this.handlePlay());
        this.videoPlayer.addEventListener('pause', () => this.handlePause());

        // Control events (only if elements exist)
        if (this.playPauseBtn) this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        if (this.seekBar) this.seekBar.addEventListener('input', (e) => this.handleSeek(e));
        if (this.volumeBar) this.volumeBar.addEventListener('input', (e) => this.handleVolumeChange(e));
        if (this.muteBtn) this.muteBtn.addEventListener('click', () => this.toggleMute());

        // Style control events (only if elements exist)
        if (this.fontFamily) this.fontFamily.addEventListener('change', () => this.updateCaptionStyle());
        if (this.fontSize) this.fontSize.addEventListener('input', (e) => this.handleFontSizeChange(e));
        if (this.textUppercase) this.textUppercase.addEventListener('change', () => this.updateCaptionStyle());
        if (this.fontColor) this.fontColor.addEventListener('change', () => this.updateCaptionStyle());
        if (this.backgroundColor) this.backgroundColor.addEventListener('change', () => this.updateCaptionStyle());
        if (this.showBackground) this.showBackground.addEventListener('change', () => this.updateCaptionStyle());
        if (this.positionX) this.positionX.addEventListener('input', (e) => this.handlePositionXChange(e));
        if (this.positionY) this.positionY.addEventListener('input', (e) => this.handlePositionYChange(e));
        if (this.captionWidth) this.captionWidth.addEventListener('input', (e) => this.handleCaptionWidthChange(e));

        // Effect events
        this.videoEffect.addEventListener('change', () => this.updateVideoEffect());
        this.effectIntensity.addEventListener('input', (e) => this.handleEffectIntensityChange(e));

        // Timeline events
        this.zoomInBtn.addEventListener('click', () => this.zoomTimeline(1.5));
        this.zoomOutBtn.addEventListener('click', () => this.zoomTimeline(0.75));
        this.resetZoomBtn.addEventListener('click', () => this.resetTimelineZoom());
        this.wordGroupingToggle.addEventListener('change', (e) => this.toggleWordGrouping(e));



            // Action button events
            this.exportBtn.addEventListener('click', () => this.exportVideo());
            this.saveProjectBtn.addEventListener('click', () => this.saveProject());

        } catch (error) {
            console.error('Failed to setup event listeners:', error);
            this.showError('Failed to setup application controls.');
        }
    }

    // Enhanced error handling
    showError(message, details = null) {
        console.error('Application Error:', message, details);

        // Update status with error
        this.updateStatus(message, 'error');

        // Show user-friendly error message
        if (this.statusInfo) {
            this.statusInfo.textContent = details || 'Please try refreshing the page.';
        }

        // Optional: Show toast notification
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info', duration = 5000) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add to page
        document.body.appendChild(toast);

        // Auto remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, duration);
    }

    // File handling methods
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.processVideoFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        this.uploadArea.classList.add('drag-over');

        // Add visual feedback
        if (this.animations.enabled) {
            this.uploadArea.style.transform = 'scale(1.02)';
        }
    }

    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();

        // Only remove drag state if leaving the upload area entirely
        if (!this.uploadArea.contains(event.relatedTarget)) {
            this.uploadArea.classList.remove('drag-over');
            this.uploadArea.style.transform = '';
        }
    }

    handleDrop(event) {
        event.preventDefault();
        event.stopPropagation();

        this.uploadArea.classList.remove('drag-over');
        this.uploadArea.style.transform = '';

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processVideoFile(files[0]);
        }
    }

    async processVideoFile(file) {
        try {
            // Validate file
            if (!this.validateVideoFile(file)) {
                return;
            }

            this.state.isProcessing = true;
            this.videoFile = file;

            // Show file info
            this.displayFileInfo(file);

            // Show progress container
            this.progressContainer.style.display = 'block';
            this.updateProgress(0, 'Preparing video...');

            // Load video for preview
            await this.loadVideoPreview(file);

            // Start transcription
            await this.transcribeVideo(file);

            // Show editor
            this.showEditor();

        } catch (error) {
            this.showError('Failed to process video file', error.message);
        } finally {
            this.state.isProcessing = false;
        }
    }

    validateVideoFile(file) {
        const maxSize = 100 * 1024 * 1024; // 100MB
        const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];

        if (!allowedTypes.includes(file.type)) {
            this.showError('Invalid file type. Please upload a video file (MP4, WebM, OGG, AVI, MOV).');
            return false;
        }

        if (file.size > maxSize) {
            this.showError('File too large. Please upload a video smaller than 100MB.');
            return false;
        }

        return true;
    }

    displayFileInfo(file) {
        const fileSize = (file.size / (1024 * 1024)).toFixed(2);
        const fileType = file.type.split('/')[1].toUpperCase();

        if (this.fileInfo) {
            this.fileInfo.innerHTML = `
                <div class="file-details">
                    <div class="file-detail">
                        <i class="fas fa-file-video"></i>
                        <span>${file.name}</span>
                    </div>
                    <div class="file-detail">
                        <i class="fas fa-weight-hanging"></i>
                        <span>${fileSize} MB</span>
                    </div>
                    <div class="file-detail">
                        <i class="fas fa-tag"></i>
                        <span>${fileType}</span>
                    </div>
                </div>
            `;
            this.fileInfo.style.display = 'block';
        }
    }

    async loadVideoPreview(file) {
        return new Promise((resolve, reject) => {
            const url = URL.createObjectURL(file);
            this.videoPlayer.src = url;

            this.videoPlayer.onloadedmetadata = () => {
                this.updateProgress(10, 'Video loaded successfully');
                resolve();
            };

            this.videoPlayer.onerror = () => {
                reject(new Error('Failed to load video'));
            };
        });
    }

    async processVideoFile(file) {
        // Validate file
        if (!file.type.startsWith('video/')) {
            this.updateStatus('Please select a valid video file', 'error');
            return;
        }

        if (file.size > 100 * 1024 * 1024) { // 100MB limit
            this.updateStatus('File size exceeds 100MB limit', 'error');
            return;
        }

        this.videoFile = file;
        this.fileInfo.textContent = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`;
        
        // Show progress
        this.progressContainer.style.display = 'block';
        this.updateStatus('Processing video...');

        try {
            // Load video for preview
            const videoURL = URL.createObjectURL(file);
            this.videoPlayer.src = videoURL;

            // Start transcription
            await this.transcribeVideo(file);

            // Switch to editor view
            this.showEditor();

        } catch (error) {
            console.error('Error processing video:', error);
            this.updateStatus('Error processing video: ' + error.message, 'error');
            this.progressContainer.style.display = 'none';
        }
    }

    async transcribeVideo(file) {
        const formData = new FormData();
        formData.append('video', file);

        try {
            this.updateProgress(20, 'Uploading video...');

            const response = await fetch('/api/transcribe', {
                method: 'POST',
                body: formData,
                // Add progress tracking for upload
                onUploadProgress: (progressEvent) => {
                    const uploadProgress = Math.round((progressEvent.loaded * 50) / progressEvent.total);
                    this.updateProgress(20 + uploadProgress, 'Uploading video...');
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Server error (${response.status}): ${errorText}`);
            }

            this.updateProgress(80, 'Processing transcription with AI...');

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            this.transcriptionData = data;
            this.state.hasTranscription = true;

            this.updateProgress(100, 'Transcription complete!');

            // Show completion animation
            if (this.animations.enabled) {
                this.progressContainer.classList.add('fade-in');
            }

            setTimeout(() => {
                this.progressContainer.style.display = 'none';
                this.progressContainer.classList.remove('fade-in');
            }, 1500);

            this.updateStatus('Video transcribed successfully');
            this.showToast('Transcription completed successfully!', 'success');

        } catch (error) {
            this.updateProgress(0, 'Transcription failed');
            throw new Error('Transcription failed: ' + error.message);
        }
    }

    showEditor() {
        // Update state
        this.state.currentView = 'editor';
        this.state.hasVideo = true;

        // Smooth transition to editor
        if (this.animations.enabled) {
            this.uploadSection.style.opacity = '0';
            this.uploadSection.style.transform = 'translateY(-20px)';

            setTimeout(() => {
                this.uploadSection.style.display = 'none';
                this.editorSection.style.display = 'flex';
                this.editorSection.style.opacity = '0';
                this.editorSection.style.transform = 'translateY(20px)';

                // Animate in editor
                requestAnimationFrame(() => {
                    this.editorSection.style.transition = 'all 0.3s ease-out';
                    this.editorSection.style.opacity = '1';
                    this.editorSection.style.transform = 'translateY(0)';
                });
            }, this.animations.duration);
        } else {
            this.uploadSection.style.display = 'none';
            this.editorSection.style.display = 'flex';
        }

        // Initialize editor components
        setTimeout(() => {
            this.initializeTimeline();
            this.initializeTranscript();
            this.setupVideoControls();

            // Enable action buttons
            if (this.exportBtn) this.exportBtn.disabled = false;
            if (this.saveProjectBtn) this.saveProjectBtn.disabled = false;

            this.updateStatus('Editor ready - Start editing your captions');
        }, this.animations.enabled ? this.animations.duration + 100 : 0);
    }

    setupVideoControls() {
        // Enhanced video control setup
        if (this.videoPlayer && this.videoPlayer.duration) {
            // Update timeline ruler
            this.updateTimelineRuler(this.videoPlayer.duration);

            // Set initial caption styling
            this.updateCaptionStyle();

            // Initialize video effects if available
            if (window.videoEffects) {
                // Effects are now automatically applied without preview controls
            }
        }
    }

    // Video control methods
    handleVideoLoaded() {
        const duration = this.videoPlayer.duration;
        if (this.totalTimeDisplay) this.totalTimeDisplay.textContent = this.formatTime(duration);
        if (this.seekBar) this.seekBar.max = duration;

        // Initialize timeline ruler
        this.updateTimelineRuler(duration);
    }

    handleTimeUpdate() {
        this.currentTime = this.videoPlayer.currentTime;
        if (this.currentTimeDisplay) this.currentTimeDisplay.textContent = this.formatTime(this.currentTime);
        if (this.seekBar) this.seekBar.value = this.currentTime;

        // Update playhead position
        this.updatePlayhead();

        // Update active caption
        this.updateActiveCaption();
    }

    handlePlay() {
        this.isPlaying = true;
        this.playPauseBtn.textContent = '⏸️';
    }

    handlePause() {
        this.isPlaying = false;
        this.playPauseBtn.textContent = '▶️';
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.videoPlayer.pause();
        } else {
            this.videoPlayer.play();
        }
    }

    handleSeek(event) {
        this.videoPlayer.currentTime = parseFloat(event.target.value);
    }

    handleVolumeChange(event) {
        this.videoPlayer.volume = parseFloat(event.target.value) / 100;
        this.updateVolumeIcon();
    }

    toggleMute() {
        this.videoPlayer.muted = !this.videoPlayer.muted;
        this.updateVolumeIcon();
    }

    updateVolumeIcon() {
        if (this.videoPlayer.muted || this.videoPlayer.volume === 0) {
            this.muteBtn.textContent = '🔇';
        } else if (this.videoPlayer.volume < 0.5) {
            this.muteBtn.textContent = '🔉';
        } else {
            this.muteBtn.textContent = '🔊';
        }
    }

    // Style control methods
    handleFontSizeChange(event) {
        this.fontSizeValue.textContent = event.target.value + 'px';
        this.updateCaptionStyle();
    }

    handlePositionXChange(event) {
        this.positionXValue.textContent = event.target.value + '%';
        this.updateCaptionStyle();
    }

    handlePositionYChange(event) {
        this.positionYValue.textContent = event.target.value + '%';
        this.updateCaptionStyle();
    }

    handleCaptionWidthChange(event) {
        this.captionWidthValue.textContent = event.target.value + '%';
        this.updateCaptionStyle();
    }

    updateCaptionStyle() {
        const overlay = this.captionOverlay;
        if (!overlay) return;

        if (this.fontFamily) overlay.style.fontFamily = this.fontFamily.value;
        if (this.fontSize) overlay.style.fontSize = this.fontSize.value + 'px';
        if (this.textUppercase) overlay.style.textTransform = this.textUppercase.checked ? 'uppercase' : 'none';
        if (this.fontColor) overlay.style.color = this.fontColor.value;
        if (this.positionX) overlay.style.left = this.positionX.value + '%';
        if (this.positionY) overlay.style.top = this.positionY.value + '%';
        if (this.captionWidth) overlay.style.maxWidth = this.captionWidth.value + '%';

        if (this.showBackground && this.backgroundColor) {
            if (this.showBackground.checked) {
                overlay.style.background = this.backgroundColor.value + 'CC'; // Add transparency
                overlay.style.padding = '10px 20px';
                overlay.style.borderRadius = '8px';
            } else {
                overlay.style.background = 'transparent';
                overlay.style.padding = '0';
                overlay.style.borderRadius = '0';
            }
        }
    }

    // Effect methods
    handleEffectIntensityChange(event) {
        this.effectIntensityValue.textContent = event.target.value + '%';
        this.updateVideoEffect();
    }

    updateVideoEffect() {
        const effect = this.videoEffect.value;
        const intensity = this.effectIntensity.value;
        let filter = 'none';

        switch (effect) {
            case 'grayscale':
                filter = `grayscale(${intensity}%)`;
                break;
            case 'sepia':
                filter = `sepia(${intensity}%)`;
                break;
            case 'blur':
                filter = `blur(${intensity / 20}px)`;
                break;
            case 'brightness':
                filter = `brightness(${intensity}%)`;
                break;
            case 'contrast':
                filter = `contrast(${intensity}%)`;
                break;
            case 'saturate':
                filter = `saturate(${intensity}%)`;
                break;
        }

        this.videoPlayer.style.filter = filter;
    }

    // Timeline methods
    initializeTimeline() {
        if (!this.transcriptionData || !this.transcriptionData.words) {
            return;
        }

        // Clear existing timeline
        this.timelineTrack.innerHTML = '<div class="timeline-playhead" id="playhead"></div>';
        this.playhead = document.getElementById('playhead');

        // Create word blocks
        const duration = this.videoPlayer.duration;
        const trackWidth = this.timelineTrack.offsetWidth - 20; // Account for padding

        // Use grouped or individual words based on toggle
        const wordsToRender = this.wordGroupingEnabled ?
            this.groupWordsByTiming(this.transcriptionData.words) :
            this.transcriptionData.words.map((word, index) => ({ ...word, originalIndex: index }));

        wordsToRender.forEach((wordGroup, index) => {
            const wordBlock = this.createWordBlock(wordGroup, index, duration, trackWidth);
            this.timelineTrack.appendChild(wordBlock);
        });

        // Initialize playhead position
        this.updatePlayhead();
    }

    groupWordsByTiming(words) {
        if (!words || words.length === 0) return [];

        const groups = [];
        let currentGroup = {
            word: words[0].word,
            start: words[0].start,
            end: words[0].end,
            originalIndex: 0,
            wordCount: 1,
            originalWords: [words[0]]
        };

        for (let i = 1; i < words.length; i++) {
            const currentWord = words[i];
            const timeBetween = currentWord.start - currentGroup.end;

            // Group words if they're close together (less than 0.5 seconds apart)
            // and the group doesn't exceed 4 words
            if (timeBetween <= 0.5 && currentGroup.wordCount < 4) {
                currentGroup.word += ' ' + currentWord.word;
                currentGroup.end = currentWord.end;
                currentGroup.wordCount++;
                currentGroup.originalWords.push(currentWord);
            } else {
                groups.push(currentGroup);
                currentGroup = {
                    word: currentWord.word,
                    start: currentWord.start,
                    end: currentWord.end,
                    originalIndex: i,
                    wordCount: 1,
                    originalWords: [currentWord]
                };
            }
        }

        groups.push(currentGroup);
        return groups;
    }

    createWordBlock(word, index, duration, trackWidth) {
        // Use the enhanced timeline editor if available
        if (window.timelineEditor && window.timelineEditor.createAdvancedWordBlock) {
            return window.timelineEditor.createAdvancedWordBlock(word, index, duration, trackWidth);
        }

        // Fallback to basic implementation
        const block = document.createElement('div');
        block.className = 'word-block';
        block.textContent = word.word;
        block.dataset.index = word.originalIndex !== undefined ? word.originalIndex : index;
        block.dataset.start = word.start;
        block.dataset.end = word.end;

        // Add word count for grouped words
        if (word.wordCount) {
            block.dataset.wordCount = word.wordCount;
            block.classList.add('grouped-word');
        }

        // Calculate position and width with zoom factor
        const zoomedTrackWidth = trackWidth * this.timelineZoom;
        const left = (word.start / duration) * zoomedTrackWidth;
        const wordDuration = word.end - word.start;
        const minWidth = word.wordCount ? Math.max(word.word.length * 6, 60) : 40;
        const width = Math.max((wordDuration / duration) * zoomedTrackWidth, minWidth);

        block.style.left = left + 'px';
        block.style.width = width + 'px';

        // Better row calculation to prevent overlaps
        const row = this.calculateWordRow(word, index, duration, trackWidth);
        block.style.top = (15 + row * 35) + 'px';

        // Add event listeners
        block.addEventListener('click', (e) => this.selectWordBlock(e, block));
        block.addEventListener('mousedown', (e) => this.startDragWordBlock(e, block));

        return block;
    }

    calculateWordRow(word, index, duration, trackWidth) {
        // Simple row calculation to minimize overlaps
        const existingBlocks = this.timelineTrack.querySelectorAll('.word-block');
        const zoomedTrackWidth = trackWidth * this.timelineZoom;
        const wordLeft = (word.start / duration) * zoomedTrackWidth;
        const wordRight = (word.end / duration) * zoomedTrackWidth;

        let row = 0;
        let foundRow = false;

        while (!foundRow && row < 6) { // Max 6 rows
            let hasOverlap = false;

            for (let block of existingBlocks) {
                const blockLeft = parseInt(block.style.left);
                const blockRight = blockLeft + parseInt(block.style.width);
                const blockRow = Math.floor((parseInt(block.style.top) - 15) / 35);

                if (blockRow === row &&
                    !(wordRight <= blockLeft || wordLeft >= blockRight)) {
                    hasOverlap = true;
                    break;
                }
            }

            if (!hasOverlap) {
                foundRow = true;
            } else {
                row++;
            }
        }

        return row;
    }

    selectWordBlock(event, block) {
        // Remove previous selection
        if (this.selectedWordBlock) {
            this.selectedWordBlock.classList.remove('selected');
        }

        // Select new block
        this.selectedWordBlock = block;
        block.classList.add('selected');

        // Seek to word time
        const startTime = parseFloat(block.dataset.start);
        this.videoPlayer.currentTime = startTime;

        const wordCount = block.dataset.wordCount ? ` (${block.dataset.wordCount} words)` : '';
        this.updateStatus(`Selected: "${block.textContent}"${wordCount} at ${startTime.toFixed(2)}s`);
    }

    startDragWordBlock(event, block) {
        event.preventDefault();
        
        const startX = event.clientX;
        const startLeft = parseInt(block.style.left);
        const trackWidth = this.timelineTrack.offsetWidth - 20;
        const duration = this.videoPlayer.duration;

        block.classList.add('dragging');

        const handleMouseMove = (e) => {
            const deltaX = e.clientX - startX;
            const newLeft = Math.max(0, Math.min(startLeft + deltaX, trackWidth - parseInt(block.style.width)));
            
            block.style.left = newLeft + 'px';
            
            // Update word timing
            const newStartTime = (newLeft / trackWidth) * duration;
            const wordDuration = parseFloat(block.dataset.end) - parseFloat(block.dataset.start);
            const newEndTime = newStartTime + wordDuration;
            
            block.dataset.start = newStartTime.toFixed(3);
            block.dataset.end = newEndTime.toFixed(3);
        };

        const handleMouseUp = () => {
            block.classList.remove('dragging');
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            
            // Update transcription data
            this.updateWordTiming(block);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    updateWordTiming(block) {
        const index = parseInt(block.dataset.index);
        const newStart = parseFloat(block.dataset.start);
        const newEnd = parseFloat(block.dataset.end);

        if (this.transcriptionData && this.transcriptionData.words[index]) {
            this.transcriptionData.words[index].start = newStart;
            this.transcriptionData.words[index].end = newEnd;
        }
    }

    updateTimelineRuler(duration) {
        this.timelineRuler.innerHTML = '';
        
        const intervals = Math.ceil(duration / 10); // 10-second intervals
        const rulerWidth = this.timelineRuler.offsetWidth;

        for (let i = 0; i <= intervals; i++) {
            const time = i * 10;
            if (time <= duration) {
                const marker = document.createElement('div');
                marker.style.position = 'absolute';
                marker.style.left = (time / duration) * rulerWidth + 'px';
                marker.style.top = '0';
                marker.style.width = '1px';
                marker.style.height = '100%';
                marker.style.background = '#4a4a6a';
                marker.style.fontSize = '0.7rem';
                marker.style.color = '#a0a0c0';
                marker.textContent = this.formatTime(time);
                marker.style.paddingLeft = '4px';
                
                this.timelineRuler.appendChild(marker);
            }
        }
    }

    updatePlayhead() {
        if (!this.videoPlayer.duration || !this.playhead) return;

        const trackWidth = this.timelineTrack.offsetWidth - 20;
        const zoomedTrackWidth = trackWidth * this.timelineZoom;
        const position = (this.currentTime / this.videoPlayer.duration) * zoomedTrackWidth;
        this.playhead.style.left = position + 'px';
    }

    zoomTimeline(factor) {
        this.timelineZoom *= factor;
        this.timelineZoom = Math.max(0.5, Math.min(this.timelineZoom, 5)); // Limit zoom range

        // Enable horizontal scrolling when zoomed in
        if (this.timelineZoom > 1) {
            this.timelineContainer.style.overflowX = 'auto';
        } else {
            this.timelineContainer.style.overflowX = 'hidden';
        }

        // Reposition word blocks based on new zoom
        this.initializeTimeline();

        // Update status with zoom level
        this.updateStatus(`Timeline zoom: ${Math.round(this.timelineZoom * 100)}%`);
    }

    resetTimelineZoom() {
        // Reset zoom to 100% and disable horizontal scrolling
        this.timelineZoom = 1;
        this.timelineContainer.style.overflowX = 'hidden';
        this.initializeTimeline();
        this.updateStatus('Timeline zoom reset to 100%');
    }



    toggleWordGrouping(event) {
        this.wordGroupingEnabled = event.target.checked;
        this.initializeTimeline();

        const status = this.wordGroupingEnabled ?
            'Word grouping enabled - words are grouped by timing' :
            'Word grouping disabled - showing individual words';
        this.updateStatus(status);
    }

    // Transcript methods
    initializeTranscript() {
        this.transcriptLoading.style.display = 'none';
        this.transcriptContent.style.display = 'block';

        if (!this.transcriptionData || !this.transcriptionData.words) {
            this.transcriptContent.innerHTML = '<p>No transcript available</p>';
            return;
        }

        // Create clickable word elements
        const wordsHTML = this.transcriptionData.words.map((word, index) => {
            return `<span class="transcript-word" data-index="${index}" data-start="${word.start}" data-end="${word.end}">${word.word}</span>`;
        }).join(' ');

        this.transcriptContent.innerHTML = wordsHTML;

        // Add click listeners to transcript words
        this.transcriptContent.querySelectorAll('.transcript-word').forEach(wordElement => {
            wordElement.addEventListener('click', (e) => {
                const startTime = parseFloat(e.target.dataset.start);
                this.videoPlayer.currentTime = startTime;
            });
        });
    }

    updateActiveCaption() {
        if (!this.transcriptionData || !this.transcriptionData.words) {
            return;
        }

        let captionText = '';

        if (this.wordGroupingEnabled) {
            // Use grouped words for caption display
            const groupedWords = this.groupWordsByTiming(this.transcriptionData.words);
            const currentGroup = groupedWords.find(group =>
                this.currentTime >= group.start && this.currentTime <= group.end
            );

            if (currentGroup) {
                captionText = currentGroup.word;
            }
        } else {
            // Use individual words for caption display
            const currentWords = this.transcriptionData.words.filter(word =>
                this.currentTime >= word.start && this.currentTime <= word.end
            );

            if (currentWords.length > 0) {
                captionText = currentWords.map(w => w.word).join(' ');
            }
        }

        // Update caption overlay
        if (captionText) {
            this.captionOverlay.textContent = captionText;
            this.captionOverlay.style.display = 'block';
        } else {
            this.captionOverlay.style.display = 'none';
        }

        // Update transcript highlighting
        this.transcriptContent.querySelectorAll('.transcript-word').forEach(wordElement => {
            const start = parseFloat(wordElement.dataset.start);
            const end = parseFloat(wordElement.dataset.end);
            
            if (this.currentTime >= start && this.currentTime <= end) {
                wordElement.classList.add('active');
            } else {
                wordElement.classList.remove('active');
            }
        });
    }

    // Export and save methods
    async exportVideo() {
        this.updateStatus('Preparing video export...');
        
        // For now, just download the transcript as JSON
        // In a full implementation, this would render the video with captions
        const exportData = {
            video: this.videoFile.name,
            transcription: this.transcriptionData,
            settings: {
                fontFamily: this.fontFamily.value,
                fontSize: this.fontSize.value,
                fontColor: this.fontColor.value,
                backgroundColor: this.backgroundColor.value,
                showBackground: this.showBackground.checked,
                positionX: this.positionX.value,
                positionY: this.positionY.value,
                captionWidth: this.captionWidth.value,
                videoEffect: this.videoEffect.value,
                effectIntensity: this.effectIntensity.value
            }
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'caption_project.json';
        a.click();
        
        URL.revokeObjectURL(url);
        this.updateStatus('Project exported successfully');
    }

    saveProject() {
        // Save current project state to localStorage
        const projectData = {
            transcription: this.transcriptionData,
            settings: {
                fontFamily: this.fontFamily.value,
                fontSize: this.fontSize.value,
                fontColor: this.fontColor.value,
                backgroundColor: this.backgroundColor.value,
                showBackground: this.showBackground.checked,
                positionX: this.positionX.value,
                positionY: this.positionY.value,
                captionWidth: this.captionWidth.value,
                videoEffect: this.videoEffect.value,
                effectIntensity: this.effectIntensity.value
            },
            timestamp: new Date().toISOString()
        };

        localStorage.setItem('captionStudioProject', JSON.stringify(projectData));
        this.updateStatus('Project saved locally');
    }

    // Enhanced utility methods
    updateProgress(percentage, text) {
        if (this.progressFill) {
            // Smooth progress animation
            this.progressFill.style.transition = 'width 0.3s ease-out';
            this.progressFill.style.width = percentage + '%';
        }

        if (this.progressText) {
            this.progressText.textContent = text;
        }

        if (this.progressPercentage) {
            this.progressPercentage.textContent = Math.round(percentage) + '%';
        }

        // Update status icon based on progress
        if (this.statusIcon) {
            if (percentage === 100) {
                this.statusIcon.className = 'fas fa-check-circle';
                this.statusIcon.style.color = 'var(--accent-success)';
            } else if (percentage > 0) {
                this.statusIcon.className = 'fas fa-spinner fa-spin';
                this.statusIcon.style.color = 'var(--accent-primary)';
            }
        }
    }

    updateStatus(message, type = 'info') {
        if (this.statusText) {
            this.statusText.textContent = message;
        }

        // Update status icon and colors based on type
        if (this.statusIcon) {
            switch (type) {
                case 'error':
                    this.statusIcon.className = 'fas fa-exclamation-circle';
                    this.statusIcon.style.color = 'var(--accent-error)';
                    if (this.statusText) this.statusText.style.color = 'var(--accent-error)';
                    break;
                case 'success':
                    this.statusIcon.className = 'fas fa-check-circle';
                    this.statusIcon.style.color = 'var(--accent-success)';
                    if (this.statusText) this.statusText.style.color = 'var(--accent-success)';
                    break;
                case 'warning':
                    this.statusIcon.className = 'fas fa-exclamation-triangle';
                    this.statusIcon.style.color = 'var(--accent-warning)';
                    if (this.statusText) this.statusText.style.color = 'var(--accent-warning)';
                    break;
                case 'processing':
                    this.statusIcon.className = 'fas fa-spinner fa-spin';
                    this.statusIcon.style.color = 'var(--accent-primary)';
                    if (this.statusText) this.statusText.style.color = 'var(--text-secondary)';
                    break;
                default: // 'info'
                    this.statusIcon.className = 'fas fa-circle';
                    this.statusIcon.style.color = 'var(--accent-primary)';
                    if (this.statusText) this.statusText.style.color = 'var(--text-secondary)';
            }
        }

        // Add timestamp to status info
        if (this.statusInfo && type !== 'processing') {
            const timestamp = new Date().toLocaleTimeString();
            this.statusInfo.textContent = `Last updated: ${timestamp}`;
        }
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
}

// Add CSS for toast notifications and enhanced UI
const appStyles = document.createElement('style');
appStyles.textContent = `
    /* Toast Notifications */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--bg-card);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--space-lg);
        box-shadow: var(--shadow-lg);
        z-index: 1000;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    }

    .toast-error {
        border-left: 4px solid var(--accent-error);
    }

    .toast-success {
        border-left: 4px solid var(--accent-success);
    }

    .toast-info {
        border-left: 4px solid var(--accent-primary);
    }

    .toast-content {
        display: flex;
        align-items: center;
        gap: var(--space-md);
        color: var(--text-primary);
    }

    .toast-close {
        background: none;
        border: none;
        color: var(--text-muted);
        cursor: pointer;
        padding: var(--space-xs);
        border-radius: var(--radius-sm);
        margin-left: auto;
        transition: all var(--transition-fast);
    }

    .toast-close:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Enhanced file info styling */
    .file-details {
        display: flex;
        flex-direction: column;
        gap: var(--space-sm);
    }

    .file-detail {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .file-detail i {
        color: var(--accent-primary);
        width: 16px;
    }

    /* Enhanced drag and drop states */
    .upload-area.drag-over {
        border-color: var(--accent-primary) !important;
        background: rgba(99, 102, 241, 0.1) !important;
        box-shadow: 0 0 30px rgba(99, 102, 241, 0.2) !important;
        transform: scale(1.02) !important;
    }

    /* Smooth transitions for all interactive elements */
    .btn, .form-control, .form-range, .upload-area, .control-panel {
        transition: all var(--transition-normal);
    }

    /* Enhanced panel animations */
    .panel-content {
        transition: all var(--transition-normal);
        overflow: hidden;
    }

    .panel-content.fade-in {
        animation: fadeIn 0.3s ease-out;
    }
`;
document.head.appendChild(appStyles);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Attempting to initialize Capsy application...');

    // First check if critical elements exist
    const uploadArea = document.getElementById('uploadArea');
    const videoPlayer = document.getElementById('videoPlayer');
    const statusText = document.getElementById('statusText');

    console.log('Critical elements check:', {
        uploadArea: !!uploadArea,
        videoPlayer: !!videoPlayer,
        statusText: !!statusText
    });

    try {
        window.captionStudio = new CaptionStudio();
        console.log('Capsy application initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Capsy application:', error);
        console.error('Error stack:', error.stack);

        // Show fallback error message
        const errorDiv = document.createElement('div');
        errorDiv.innerHTML = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--bg-card);
                padding: 2rem;
                border-radius: var(--radius-lg);
                border: 1px solid var(--accent-error);
                color: var(--text-primary);
                text-align: center;
                z-index: 9999;
            ">
                <h3 style="color: var(--accent-error); margin-bottom: 1rem;">
                    <i class="fas fa-exclamation-triangle"></i>
                    Application Error
                </h3>
                <p>Failed to initialize Capsy. Please refresh the page and try again.</p>
                <button onclick="location.reload()" style="
                    margin-top: 1rem;
                    padding: 0.5rem 1rem;
                    background: var(--accent-primary);
                    color: white;
                    border: none;
                    border-radius: var(--radius-md);
                    cursor: pointer;
                ">
                    Refresh Page
                </button>
            </div>
        `;
        document.body.appendChild(errorDiv);
    }
});


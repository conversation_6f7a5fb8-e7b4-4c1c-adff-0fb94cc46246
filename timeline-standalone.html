<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timeline Editor - Standalone</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* CSS Variables */
        :root {
            --bg-primary: #0a0a0f;
            --bg-secondary: #12121a;
            --bg-tertiary: #1a1a26;
            --bg-card: #1e1e2e;
            --bg-hover: #252538;
            --accent-primary: #6366f1;
            --accent-secondary: #8b5cf6;
            --accent-tertiary: #06b6d4;
            --accent-success: #10b981;
            --accent-warning: #f59e0b;
            --accent-error: #ef4444;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --text-disabled: #475569;
            --border-primary: #334155;
            --border-secondary: #1e293b;
            --border-accent: #6366f1;
            --gradient-primary: linear-gradient(135deg, #6366f1, #8b5cf6);
            --gradient-secondary: linear-gradient(135deg, #06b6d4, #3b82f6);
            --gradient-accent: linear-gradient(135deg, #8b5cf6, #ec4899);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 250ms ease-in-out;
            --transition-slow: 350ms ease-in-out;
        }
        /* Reset */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        /* Base Styles */
        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }
        body {
            font-family: var(--font-family);
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: var(--space-xl);
        }
        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        /* Timeline Section */
        .timeline-section {
            background: var(--bg-card);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
        }
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid var(--border-secondary);
        }
        .timeline-header h3 {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        .timeline-controls {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }
        .word-grouping-toggle {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            margin-right: var(--space-sm);
        }
        .toggle-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            white-space: nowrap;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-tertiary);
            transition: var(--transition-fast);
            border-radius: 20px;
            border: 1px solid var(--border-secondary);
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: var(--text-muted);
            transition: var(--transition-fast);
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: var(--accent-primary);
            border-color: var(--accent-primary);
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
            background-color: white;
        }
        .btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-secondary);
            color: var(--text-secondary);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
        }
        .btn:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            border-color: var(--border-primary);
        }
        .btn-sm {
            padding: var(--space-xs);
            font-size: 0.75rem;
        }
        .timeline-container {
            position: relative;
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            min-height: 80px;
            overflow: hidden;
            user-select: none;
        }
        .timeline-ruler {
            height: 20px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-secondary);
            position: relative;
        }
        .timeline-track {
            height: 60px;
            position: relative;
            padding: var(--space-sm);
            overflow-x: auto; /* Enable horizontal scrolling */
            scroll-behavior: smooth;
        }
        .timeline-playhead {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--accent-primary);
            z-index: 10;
            pointer-events: none;
            box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
        /* Word Blocks */
        .word-block {
            position: absolute;
            background: var(--gradient-primary);
            color: white;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            user-select: none;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }
        .word-block:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        .word-block.selected {
            background: var(--gradient-accent);
            box-shadow: var(--shadow-lg);
        }
        .word-block.grouped-word {
            background: linear-gradient(135deg, var(--accent-secondary), var(--accent-tertiary));
            border: 1px solid rgba(139, 92, 246, 0.3);
            font-weight: 500;
        }
        .word-block.grouped-word:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
        }
        .word-block.grouped-word.selected {
            background: linear-gradient(135deg, var(--accent-warning), var(--accent-error));
            border-color: var(--accent-warning);
        }
        /* Status */
        .status-bar {
            margin-top: var(--space-lg);
            padding: var(--space-sm) var(--space-md);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="timeline-section">
            <div class="timeline-header">
                <h3><i class="fas fa-timeline"></i> Timeline Editor</h3>
                <div class="timeline-controls">
                    <button class="btn btn-sm" id="addWordBtn" title="Add Word">
                        <i class="fas fa-plus"></i> Add Word
                    </button>
                    <div class="word-grouping-toggle">
                        <label class="toggle-switch" title="Group words by timing">
                            <input type="checkbox" id="wordGroupingToggle" checked>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">Group Words</span>
                    </div>
                    <button class="btn btn-sm" id="zoomInBtn" title="Zoom In">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="btn btn-sm" id="zoomOutBtn" title="Zoom Out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button class="btn btn-sm" id="resetZoomBtn" title="Reset Zoom">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                </div>
            </div>
            <div class="timeline-container" id="timelineContainer">
                <div class="timeline-ruler" id="timelineRuler"></div>
                <div class="timeline-track" id="timelineTrack">
                    <div class="timeline-playhead" id="playhead"></div>
                </div>
            </div>
        </div>
        <div class="status-bar" id="statusBar">
            Ready - Click on word blocks to select and edit them
        </div>
    </div>
    <script>
        // Timeline Editor Class
        class TimelineEditor {
            constructor() {
                this.timelineContainer = document.getElementById('timelineContainer');
                this.timelineTrack = document.getElementById('timelineTrack');
                this.timelineRuler = document.getElementById('timelineRuler');
                this.playhead = document.getElementById('playhead');
                this.statusBar = document.getElementById('statusBar');
                this.wordGroupingToggle = document.getElementById('wordGroupingToggle');
                // Timeline state
                this.duration = 60; // 60 seconds for demo
                this.currentTime = 0;
                this.zoomLevel = 1;
                this.selectedWordBlock = null;
                this.isDragging = false;
                this.wordGroupingEnabled = true; // Enabled by default
                // Placeholder speech data
                this.speechData = [
                    { text: "Hello", start: 0.5, end: 1.2 },
                    { text: "and", start: 1.3, end: 1.5 },
                    { text: "welcome", start: 1.6, end: 2.3 },
                    { text: "to", start: 2.4, end: 2.6 },
                    { text: "our", start: 2.7, end: 3.0 },
                    { text: "video", start: 3.1, end: 3.6 },
                    { text: "caption", start: 3.7, end: 4.3 },
                    { text: "system.", start: 4.4, end: 5.1 },
                    { text: "This", start: 6.0, end: 6.4 },
                    { text: "timeline", start: 6.5, end: 7.2 },
                    { text: "editor", start: 7.3, end: 7.9 },
                    { text: "allows", start: 8.0, end: 8.5 },
                    { text: "you", start: 8.6, end: 8.9 },
                    { text: "to", start: 9.0, end: 9.2 },
                    { text: "precisely", start: 9.3, end: 10.1 },
                    { text: "control", start: 10.2, end: 10.8 },
                    { text: "the", start: 10.9, end: 11.1 },
                    { text: "timing", start: 11.2, end: 11.8 },
                    { text: "of", start: 11.9, end: 12.1 },
                    { text: "each", start: 12.2, end: 12.6 },
                    { text: "word.", start: 12.7, end: 13.3 },
                    { text: "You", start: 15.0, end: 15.3 },
                    { text: "can", start: 15.4, end: 15.7 },
                    { text: "drag", start: 15.8, end: 16.2 },
                    { text: "and", start: 16.3, end: 16.5 },
                    { text: "drop", start: 16.6, end: 17.0 },
                    { text: "words", start: 17.1, end: 17.6 },
                    { text: "to", start: 17.7, end: 17.9 },
                    { text: "adjust", start: 18.0, end: 18.6 },
                    { text: "their", start: 18.7, end: 19.1 },
                    { text: "position", start: 19.2, end: 19.9 },
                    { text: "in", start: 20.0, end: 20.2 },
                    { text: "time.", start: 20.3, end: 20.9 },
                    { text: "The", start: 22.0, end: 22.3 },
                    { text: "interface", start: 22.4, end: 23.2 },
                    { text: "is", start: 23.3, end: 23.5 },
                    { text: "intuitive", start: 23.6, end: 24.4 },
                    { text: "and", start: 24.5, end: 24.7 },
                    { text: "responsive,", start: 24.8, end: 25.6 },
                    { text: "making", start: 25.7, end: 26.3 },
                    { text: "caption", start: 26.4, end: 27.0 },
                    { text: "editing", start: 27.1, end: 27.7 },
                    { text: "a", start: 27.8, end: 27.9 },
                    { text: "breeze.", start: 28.0, end: 28.7 },
                    { text: "Try", start: 30.0, end: 30.4 },
                    { text: "clicking", start: 30.5, end: 31.2 },
                    { text: "on", start: 31.3, end: 31.5 },
                    { text: "different", start: 31.6, end: 32.4 },
                    { text: "words", start: 32.5, end: 33.0 },
                    { text: "to", start: 33.1, end: 33.3 },
                    { text: "select", start: 33.4, end: 34.0 },
                    { text: "them", start: 34.1, end: 34.5 },
                    { text: "and", start: 34.6, end: 34.8 },
                    { text: "see", start: 34.9, end: 35.2 },
                    { text: "the", start: 35.3, end: 35.5 },
                    { text: "interactive", start: 35.6, end: 36.5 },
                    { text: "features", start: 36.6, end: 37.3 },
                    { text: "in", start: 37.4, end: 37.6 },
                    { text: "action.", start: 37.7, end: 38.4 }
                ];
                this.init();
            }
            init() {
                this.setupEventListeners();
                this.updateTimelineRuler();
                this.createWordBlocks();
                this.updateStatus("Timeline loaded with placeholder speech data");
            }
            setupEventListeners() {
                // Word grouping toggle
                this.wordGroupingToggle.addEventListener('change', () => {
                    this.wordGroupingEnabled = this.wordGroupingToggle.checked;
                    this.createWordBlocks();
                });
                // Zoom controls
                document.getElementById('zoomInBtn').addEventListener('click', () => this.zoomIn());
                document.getElementById('zoomOutBtn').addEventListener('click', () => this.zoomOut());
                document.getElementById('resetZoomBtn').addEventListener('click', () => this.resetZoom());
                document.getElementById('addWordBtn').addEventListener('click', () => this.addWord());
                // Timeline click to seek
                this.timelineTrack.addEventListener('click', (e) => this.handleTimelineClick(e));
                // Mouse wheel for zoom
                this.timelineContainer.addEventListener('wheel', (e) => this.handleWheelZoom(e));
            }
            updateTimelineRuler() {
                this.timelineRuler.innerHTML = '';
                const rulerWidth = this.timelineRuler.offsetWidth;
                const markerInterval = Math.max(1, Math.floor(this.duration / 10 / this.zoomLevel)); // Adjust interval based on zoom
                for (let i = 0; i <= this.duration; i += markerInterval) {
                    const time = i;
                    if (time <= this.duration) {
                        const marker = document.createElement('div');
                        marker.style.position = 'absolute';
                        marker.style.left = (time / this.duration) * rulerWidth + 'px';
                        marker.style.top = '0';
                        marker.style.width = '1px';
                        marker.style.height = '100%';
                        marker.style.background = '#4a4a6a';
                        marker.style.fontSize = '0.7rem';
                        marker.style.color = '#a0a0c0';
                        marker.textContent = this.formatTime(time);
                        marker.style.paddingLeft = '4px';
                        this.timelineRuler.appendChild(marker);
                    }
                }
            }
            createWordBlocks() {
                // Clear existing blocks (except playhead)
                const existingBlocks = this.timelineTrack.querySelectorAll('.word-block');
                existingBlocks.forEach(block => block.remove());
                const trackWidth = this.timelineTrack.offsetWidth - 20; // Account for padding
                // Use grouped or individual words based on toggle
                const wordsToRender = this.wordGroupingEnabled ?
                    this.groupWordsByTiming(this.speechData) :
                    this.speechData;
                wordsToRender.forEach((wordData, index) => {
                    const block = document.createElement('div');
                    block.className = 'word-block' + (this.wordGroupingEnabled ? ' grouped-word' : '');
                    block.textContent = wordData.text;
                    block.dataset.start = wordData.start;
                    block.dataset.end = wordData.end;
                    block.dataset.index = index;
                    // Position and size the block
                    const startPercent = (wordData.start / this.duration) * 100;
                    const duration = wordData.end - wordData.start;
                    const widthPercent = (duration / this.duration) * 100;
                    block.style.left = `${startPercent}%`;
                    block.style.width = `${Math.max(widthPercent, 2)}%`; // Minimum 2% width
                    block.style.top = '50%';
                    block.style.transform = 'translateY(-50%)';
                    // Add event listeners
                    this.setupWordBlockEvents(block);
                    this.timelineTrack.appendChild(block);
                });
            }
            groupWordsByTiming(words) {
                // Simple grouping: combine words that are close together (within 0.3 seconds)
                const grouped = [];
                let currentGroup = null;
                words.forEach(word => {
                    if (!currentGroup || word.start - currentGroup.end > 0.3) {
                        // Start new group
                        currentGroup = {
                            text: word.text,
                            start: word.start,
                            end: word.end
                        };
                        grouped.push(currentGroup);
                    } else {
                        // Add to current group
                        currentGroup.text += ' ' + word.text;
                        currentGroup.end = word.end;
                    }
                });
                return grouped;
            }
            setupWordBlockEvents(block) {
                // Click to select
                block.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.selectWordBlock(block);
                });
                // Double-click to edit
                block.addEventListener('dblclick', (e) => {
                    e.stopPropagation();
                    this.editWordBlock(block);
                });
                // Drag functionality
                block.addEventListener('mousedown', (e) => {
                    if (e.button === 0) { // Left mouse button
                        this.startDrag(e, block);
                    }
                });
                // Context menu
                block.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    this.showContextMenu(e, block);
                });
            }
            selectWordBlock(block) {
                // Remove previous selection
                if (this.selectedWordBlock) {
                    this.selectedWordBlock.classList.remove('selected');
                    this.selectedWordBlock.style.transform = this.selectedWordBlock.style.transform.replace('translateY(-2px)', '');
                    this.selectedWordBlock.style.boxShadow = '';
                }
                // Select new block
                this.selectedWordBlock = block;
                block.classList.add('selected');
                const currentTransform = block.style.transform || '';
                if (!currentTransform.includes('translateY(-2px)')) {
                    block.style.transform = currentTransform + ' translateY(-2px)';
                }
                block.style.boxShadow = '0 0 0 2px #4db8ff, 0 4px 12px rgba(77, 184, 255, 0.4)';
                // Update playhead position
                const startTime = parseFloat(block.dataset.start);
                this.currentTime = startTime;
                this.updatePlayhead();
                // Update status
                this.updateStatus(`Selected: "${block.textContent}" (${startTime.toFixed(2)}s - ${parseFloat(block.dataset.end).toFixed(2)}s)`);
            }
            editWordBlock(block) {
                const currentText = block.textContent;
                const input = document.createElement('input');
                input.type = 'text';
                input.value = currentText;
                input.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: #1a1a2a;
                    color: white;
                    border: 2px solid #4db8ff;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 0.75rem;
                    font-weight: 500;
                    z-index: 100;
                `;
                block.style.position = 'relative';
                block.appendChild(input);
                input.focus();
                input.select();
                const finishEdit = () => {
                    const newText = input.value.trim();
                    if (newText) {
                        block.textContent = newText;
                        this.updateStatus(`Updated word: "${newText}"`);
                    } else {
                        block.textContent = currentText;
                    }
                    input.remove();
                };
                input.addEventListener('blur', finishEdit);
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        finishEdit();
                    } else if (e.key === 'Escape') {
                        block.textContent = currentText;
                        input.remove();
                    }
                });
            }
            startDrag(e, block) {
                this.isDragging = true;
                this.selectWordBlock(block);
                const startX = e.clientX;
                const trackRect = this.timelineTrack.getBoundingClientRect();
                const trackWidth = this.timelineTrack.offsetWidth - 20;
                const startLeft = parseFloat(block.style.left) * trackWidth / 100;
                // Create ghost element
                const ghost = block.cloneNode(true);
                ghost.style.opacity = '0.5';
                ghost.style.zIndex = '1000';
                this.timelineTrack.appendChild(ghost);
                const handleMouseMove = (e) => {
                    const deltaX = e.clientX - startX;
                    const newLeft = Math.max(0, Math.min(startLeft + deltaX, trackWidth - parseInt(block.offsetWidth)));
                    const newLeftPercent = (newLeft / trackWidth) * 100;
                    block.style.left = `${newLeftPercent}%`;
                    ghost.style.left = `${newLeftPercent}%`;
                    // Update timing preview
                    const newStartTime = (newLeft / trackWidth) * this.duration;
                    const wordDuration = parseFloat(block.dataset.end) - parseFloat(block.dataset.start);
                    const newEndTime = newStartTime + wordDuration;
                    this.showTimingPreview(block, newStartTime, newEndTime);
                };
                const handleMouseUp = () => {
                    this.isDragging = false;
                    ghost.remove();
                    this.hideTimingPreview();
                    // Update word timing
                    const newLeft = parseFloat(block.style.left) * (this.timelineTrack.offsetWidth - 20) / 100;
                    const newStartTime = (newLeft / (this.timelineTrack.offsetWidth - 20)) * this.duration;
                    const wordDuration = parseFloat(block.dataset.end) - parseFloat(block.dataset.start);
                    const newEndTime = newStartTime + wordDuration;
                    block.dataset.start = newStartTime.toFixed(3);
                    block.dataset.end = newEndTime.toFixed(3);
                    this.updateStatus(`Moved "${block.textContent}" to ${newStartTime.toFixed(2)}s - ${newEndTime.toFixed(2)}s`);
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                };
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                e.preventDefault();
            }
            showTimingPreview(block, startTime, endTime) {
                let preview = document.querySelector('.timing-preview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.className = 'timing-preview';
                    preview.style.cssText = `
                        position: fixed;
                        background: #1a1a2a;
                        color: #4db8ff;
                        padding: 8px 12px;
                        border-radius: 6px;
                        font-size: 0.8rem;
                        font-weight: 600;
                        border: 1px solid #3a3a5a;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                        z-index: 1002;
                        pointer-events: none;
                    `;
                    document.body.appendChild(preview);
                }
                preview.textContent = `${startTime.toFixed(2)}s - ${endTime.toFixed(2)}s`;
                const rect = block.getBoundingClientRect();
                preview.style.left = rect.left + 'px';
                preview.style.top = (rect.top - 40) + 'px';
            }
            hideTimingPreview() {
                const preview = document.querySelector('.timing-preview');
                if (preview) {
                    preview.remove();
                }
            }
            showContextMenu(event, block) {
                // Remove existing context menu
                const existingMenu = document.querySelector('.timeline-context-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }
                const menu = document.createElement('div');
                menu.className = 'timeline-context-menu';
                menu.style.cssText = `
                    position: fixed;
                    top: ${event.clientY}px;
                    left: ${event.clientX}px;
                    background: #1a1a2a;
                    border: 1px solid #3a3a5a;
                    border-radius: 6px;
                    padding: 8px 0;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 1000;
                    min-width: 150px;
                `;
                const menuItems = [
                    { text: 'Edit Text', action: () => this.editWordBlock(block) },
                    { text: 'Delete Word', action: () => this.deleteWordBlock(block) },
                    { text: 'Split Word', action: () => this.splitWordBlock(block) },
                    { text: 'Duplicate', action: () => this.duplicateWordBlock(block) }
                ];
                menuItems.forEach(item => {
                    const menuItem = document.createElement('div');
                    menuItem.textContent = item.text;
                    menuItem.style.cssText = `
                        padding: 8px 16px;
                        cursor: pointer;
                        color: #cbd5e1;
                        font-size: 0.875rem;
                        transition: background-color 0.15s;
                    `;
                    menuItem.addEventListener('mouseenter', () => {
                        menuItem.style.backgroundColor = '#252538';
                    });
                    menuItem.addEventListener('mouseleave', () => {
                        menuItem.style.backgroundColor = 'transparent';
                    });
                    menuItem.addEventListener('click', () => {
                        item.action();
                        menu.remove();
                    });
                    menu.appendChild(menuItem);
                });
                document.body.appendChild(menu);
                // Remove menu when clicking elsewhere
                setTimeout(() => {
                    document.addEventListener('click', () => {
                        if (menu.parentNode) {
                            menu.remove();
                        }
                    }, { once: true });
                }, 0);
            }
            deleteWordBlock(block) {
                if (confirm(`Delete word "${block.textContent}"?`)) {
                    block.remove();
                    if (this.selectedWordBlock === block) {
                        this.selectedWordBlock = null;
                    }
                    this.updateStatus(`Deleted word: "${block.textContent}"`);
                }
            }
            splitWordBlock(block) {
                const text = block.textContent;
                const words = text.split(' ');
                if (words.length > 1) {
                    const startTime = parseFloat(block.dataset.start);
                    const endTime = parseFloat(block.dataset.end);
                    const duration = endTime - startTime;
                    const wordDuration = duration / words.length;
                    // Remove original block
                    block.remove();
                    // Create new blocks for each word
                    words.forEach((word, index) => {
                        const newBlock = document.createElement('div');
                        newBlock.className = 'word-block';
                        newBlock.textContent = word;
                        newBlock.dataset.start = (startTime + index * wordDuration).toFixed(3);
                        newBlock.dataset.end = (startTime + (index + 1) * wordDuration).toFixed(3);
                        const startPercent = ((startTime + index * wordDuration) / this.duration) * 100;
                        const widthPercent = (wordDuration / this.duration) * 100;
                        newBlock.style.left = `${startPercent}%`;
                        newBlock.style.width = `${Math.max(widthPercent, 2)}%`;
                        newBlock.style.top = '50%';
                        newBlock.style.transform = 'translateY(-50%)';
                        this.setupWordBlockEvents(newBlock);
                        this.timelineTrack.appendChild(newBlock);
                    });
                    this.updateStatus(`Split "${text}" into ${words.length} words`);
                } else {
                    this.updateStatus('Cannot split single word');
                }
            }
            duplicateWordBlock(block) {
                const newBlock = block.cloneNode(true);
                const startTime = parseFloat(block.dataset.end) + 0.1; // Place after original
                const duration = parseFloat(block.dataset.end) - parseFloat(block.dataset.start);
                const endTime = startTime + duration;
                newBlock.dataset.start = startTime.toFixed(3);
                newBlock.dataset.end = endTime.toFixed(3);
                const startPercent = (startTime / this.duration) * 100;
                const widthPercent = (duration / this.duration) * 100;
                newBlock.style.left = `${startPercent}%`;
                newBlock.style.width = `${Math.max(widthPercent, 2)}%`;
                // Remove any selection styling
                newBlock.classList.remove('selected');
                newBlock.style.transform = 'translateY(-50%)';
                newBlock.style.boxShadow = '';
                this.setupWordBlockEvents(newBlock);
                this.timelineTrack.appendChild(newBlock);
                this.updateStatus(`Duplicated word: "${block.textContent}"`);
            }
            handleTimelineClick(event) {
                // Don't seek if clicking on a word block
                if (event.target.classList.contains('word-block')) {
                    return;
                }
                const rect = this.timelineTrack.getBoundingClientRect();
                const clickX = event.clientX - rect.left - 10; // Account for padding
                const trackWidth = this.timelineTrack.offsetWidth - 20;
                if (trackWidth > 0) {
                    const clickTime = (clickX / trackWidth) * this.duration;
                    this.currentTime = Math.max(0, Math.min(clickTime, this.duration));
                    this.updatePlayhead();
                    this.updateStatus(`Seeked to ${this.currentTime.toFixed(2)}s`);
                }
            }
            handleWheelZoom(event) {
                event.preventDefault();
                if (event.deltaY < 0) {
                    this.zoomIn();
                } else {
                    this.zoomOut();
                }
            }
            zoomIn() {
                this.zoomLevel = Math.min(this.zoomLevel * 1.2, 5);
                this.applyZoom();
                this.updateStatus(`Zoom: ${Math.round(this.zoomLevel * 100)}%`);
            }
            zoomOut() {
                this.zoomLevel = Math.max(this.zoomLevel / 1.2, 0.2);
                this.applyZoom();
                this.updateStatus(`Zoom: ${Math.round(this.zoomLevel * 100)}%`);
            }
            resetZoom() {
                this.zoomLevel = 1;
                this.applyZoom();
                this.updateStatus('Zoom reset to 100%');
            }
            applyZoom() {
                const scale = this.zoomLevel;
                this.timelineTrack.style.transform = `scaleX(${scale})`;
                this.timelineRuler.style.transform = `scaleX(${scale})`;
                // Update ruler after zoom
                setTimeout(() => this.updateTimelineRuler(), 0);
            }
            updatePlayhead() {
                const position = this.currentTime * 100 + 10; // Account for padding
                this.playhead.style.left = position + 'px';
            }
            updateStatus(message) {
                this.statusBar.textContent = message;
            }
            formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            }
        }
        // Initialize timeline editor when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.timelineEditor = new TimelineEditor();
        });
    </script>
</body>
</html>